/* أنماط ملخص الطلب مع زر التبديل - تصميم مبسط بدون هوامش */
.total-price-container {
    border: 1px solid var(--form-border-color, #e2e8f0);
    border-radius: var(--form-radius, 8px);
    overflow: hidden;
    margin: 0;
    box-shadow: none;
    transition: all 0.3s ease;
}

.total-price-container:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: var(--form-input-bg, #f1f5f9);
    border-bottom: 1px solid var(--form-border-color, #e2e8f0);
    cursor: pointer;
    margin: 0;
}

.summary-title {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--form-text-color, #1e293b);
    display: flex;
    align-items: center;
    gap: 4px;
    white-space: nowrap;
}

.summary-title i {
    color: var(--form-primary-color, #3730a3);
    font-size: 13px;
}

.summary-toggle-btn {
    background: none;
    border: none;
    color: var(--form-placeholder-color, #64748b);
    cursor: pointer;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.summary-toggle-btn:hover {
    background-color: var(--form-border-color, #e2e8f0);
    color: var(--form-text-color, #1e293b);
}

.summary-toggle-btn i {
    transition: transform 0.3s ease;
}

.summary-toggle-btn.collapsed i {
    transform: rotate(180deg);
}

.price-details {
    padding: 8px 12px;
    background-color: var(--form-bg-color, #fff);
    transition: max-height 0.3s ease, opacity 0.3s ease, padding 0.3s ease;
    overflow: hidden;
    margin: 0;
}

.price-details.collapsed {
    max-height: 0;
    padding: 0;
    opacity: 0;
}

.price-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 0 6px 0;
    padding: 4px 0;
    border-bottom: 1px dashed var(--form-border-color, #e2e8f0);
}

.price-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.price-row.total {
    font-weight: bold;
    color: var(--form-text-color, #1e293b);
    border-top: 1px solid var(--form-border-color, #e2e8f0);
    padding: 6px 0 0 0;
    margin: 4px 0 0 0;
}

.price-label {
    color: var(--form-label-color, #475569);
    display: flex;
    align-items: center;
    gap: 4px;
    font-weight: 500;
    font-size: 12px;
}

.price-label i {
    color: var(--form-primary-color, #3730a3);
    width: 16px;
    text-align: center;
    font-size: 12px;
}

.price-row.total .price-label i {
    color: var(--form-primary-color, #3730a3);
}

.product-price-display,
.shipping-price-display,
.total-price-display {
    font-weight: 600;
    color: var(--form-primary-color, #3730a3);
    background-color: rgba(var(--form-primary-rgb, 55, 48, 163), 0.05);
    padding: 2px 6px;
    border-radius: var(--form-input-radius, 4px);
    transition: all 0.3s ease;
    font-size: 12px;
}

.product-regular-price {
    font-size: 10px;
    color: #bbb;
    text-decoration: line-through;
    font-weight: 300;
    opacity: 0.8;
    margin-right: 6px;
    display: inline-block;
}

.product-price-display:hover,
.shipping-price-display:hover,
.total-price-display:hover {
    background-color: rgba(var(--form-primary-rgb, 55, 48, 163), 0.1);
}

.total-price-display {
    color: var(--form-primary-color, #3730a3);
    font-size: 13px;
    background-color: rgba(var(--form-primary-rgb, 55, 48, 163), 0.08);
    padding: 3px 6px;
}

/* إعدادات الطي الافتراضي لملخص الطلب */
.total-price-container.collapsed-default .price-details {
    display: none;
}

.total-price-container.collapsed-default .summary-toggle-btn i {
    transform: rotate(180deg);
}

/* إزالة الهوامش والفراغات المزعجة - تصميم مبسط */
.total-price-container * {
    box-sizing: border-box;
}

/* إزالة الهوامش الخارجية للحاوي الرئيسي */
.pexlat-form-shortcode-container .total-price-container,
.pexlat-form-container .total-price-container {
    margin: 0 !important;
    margin-bottom: 0 !important;
}

/* تبسيط شارة الكمية */
.product-quantity-badge {
    font-size: 10px;
    background-color: rgba(var(--form-primary-rgb, 55, 48, 163), 0.1);
    color: var(--form-primary-color, #3730a3);
    padding: 1px 4px;
    border-radius: 3px;
    margin-left: 4px;
    font-weight: 500;
}

/* تحسين طرق الشحن المبسطة */
.simple-shipping-methods-container {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.simple-shipping-methods-container .shipping-method-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 2px 0;
    font-size: 11px;
}

.simple-shipping-methods-container .shipping-method-option input[type="radio"] {
    margin: 0 4px 0 0;
    transform: scale(0.8);
}

/* إزالة الفراغات من العناصر الفرعية */
.price-details > *:first-child {
    margin-top: 0 !important;
}

.price-details > *:last-child {
    margin-bottom: 0 !important;
}

/* قواعد إضافية لضمان التصميم المبسط */
.total-price-container,
.total-price-container * {
    box-sizing: border-box !important;
}

/* إزالة جميع الهوامش الخارجية للحاوي */
.pexlat-form-container .total-price-container,
.pexlat-form-shortcode-container .total-price-container,
.pexlat-form-form .total-price-container {
    margin: 0 !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
}

/* إزالة الفراغات الداخلية الزائدة */
.total-price-container .summary-header {
    margin: 0 !important;
}

.total-price-container .price-details {
    margin: 0 !important;
}

/* تحسين المظهر العام */
.total-price-container {
    border-radius: 6px !important;
    overflow: hidden !important;
}

/* إزالة الظلال المزعجة */
.total-price-container,
.total-price-container:hover {
    box-shadow: none !important;
}

/* تحسين الحدود */
.total-price-container {
    border: 1px solid var(--form-border-color, #e2e8f0) !important;
}

/* قواعد نهائية لضمان التصميم المبسط تماماً */
/* إزالة جميع الهوامش من أي مكان قد تأتي منه */
.pexlat-form-container .total-price-container,
.pexlat-form-shortcode-container .total-price-container,
.pexlat-form-form .total-price-container,
.woocommerce .total-price-container,
div.total-price-container,
section.total-price-container {
    margin: 0 !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding: 0 !important;
}

/* إزالة الهوامش من العناصر الداخلية */
.total-price-container .summary-header,
.total-price-container .price-details,
.total-price-container .price-row,
.total-price-container .price-label,
.total-price-container .summary-title {
    margin: 0 !important;
}

/* ضبط الحشو الداخلي فقط حيث نحتاجه */
.total-price-container .summary-header {
    padding: 8px 12px !important;
}

.total-price-container .price-details {
    padding: 2px 12px !important;
}

.total-price-container .price-row {
    padding: 4px 0 !important;
    margin: 0 0 6px 0 !important;
}

.total-price-container .price-row:last-child {
    margin-bottom: 0 !important;
}

.total-price-container .price-row.total {
    padding: 6px 0 0 0 !important;
    margin: 4px 0 0 0 !important;
}

/* إزالة أي فراغات إضافية من المحتوى */
.total-price-container * {
    box-sizing: border-box !important;
}

/* ضمان عدم وجود فراغات خارجية */
.total-price-container::before,
.total-price-container::after {
    display: none !important;
}

/* إزالة أي تأثيرات قد تضيف مساحة */
.total-price-container {
    clear: none !important;
    float: none !important;
    position: relative !important;
}
